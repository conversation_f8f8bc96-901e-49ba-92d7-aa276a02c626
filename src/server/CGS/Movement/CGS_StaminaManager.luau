--!strict

--[[
  - file: CGS_StaminaManager.lua

  - version: 1.0.0
  - author: BleckWolf25
  - contributors: Z

  - copyright: Dynamic Innovative Studio

  - description:
    - Server-side modulescript stamina manager for CGS Movement subsystem
    - Manages stamina per player with drain & regen logic
    - Provides API for stamina requests (sprint, jump)
    - Sends updates to clients for stamina synchronization
]]

-- ============================================================================
-- SERVICES
-- ============================================================================
local Players: Players = game:GetService("Players")
local ReplicatedStorage = game:GetService("ReplicatedStorage")

-- ============================================================================
-- MODULES
-- ============================================================================
local Configuration =
  require(ReplicatedStorage.Configurations.Systems:WaitForChild("CGS_Configuration"))
local Utils = require(ReplicatedStorage.CGS.Shared:WaitForChild("CGS_Utils"))

-- ============================================================================
-- REFERENCES & CONSTANTS
-- ============================================================================
local Config = Configuration.Config.Movement
local TAG = "CGS_StaminaManager" :: string

-- Exhaustion Config
local exhaustionConfig = {
  depletionWindow = Config.DepletionWindow,
  maxDepletions = Config.MaxDepletions,
  exhaustDuration = Config.ExhaustDuration,
  regenMultiplierWhenExhausted = Config.RegenMultiplierExhausted,
}

-- ============================================================================
-- REMOTE EVENTS
-- ============================================================================
local staminaUpdate = ReplicatedStorage.CGS.Remotes:WaitForChild("staminaUpdate") :: RemoteEvent
local exhaustEvent = ReplicatedStorage.CGS.Remotes:WaitForChild("exhaustEvent") :: RemoteEvent
local jumpRequest = ReplicatedStorage.CGS.Remotes:WaitForChild("jumpRequest") :: RemoteEvent

-- ============================================================================
-- MAIN IMPLEMENTATION
-- ============================================================================
local StaminaManager = {}

-- ============================================================================
-- STATES
-- ============================================================================

-- Table to store stamina values in memory for quick access
local playerStamina = {} :: { [Player]: number }

-- Table to store exhaustion values for quick access
local exhaustionData = {} :: {
  [Player]: {
    timestamps: { number },
    isExhausted: boolean,
    exhaustEndTime: number,
  },
}

-- ============================================================================
-- PRIVATE METHODS
-- ============================================================================

-- Create stamina NumberValue in player and memory
local function createStaminaForPlayer(player: Player)
  -- CGS Folder
  local cgsFolder = player:FindFirstChild("CGS") or Instance.new("Folder")
  if not cgsFolder:IsA("Folder") then
    return
  end
  cgsFolder.Name = "CGS"
  cgsFolder.Parent = player

  -- Movement Folder
  local movementFolder = cgsFolder:FindFirstChild("Movement") or Instance.new("Folder")
  if not movementFolder:IsA("Folder") then
    return
  end
  movementFolder.Name = "Movement"
  movementFolder.Parent = cgsFolder

  -- Stamina Value
  local staminaValue = movementFolder:FindFirstChild("Stamina") or Instance.new("NumberValue")
  if not staminaValue:IsA("NumberValue") then
    return
  end
  staminaValue.Name = "Stamina"
  staminaValue.Value = Config.MaxStamina
  staminaValue.Parent = movementFolder

  -- Exhausted Value
  local exhaustedValue = movementFolder:FindFirstChild("Exhausted") or Instance.new("BoolValue")
  if not exhaustedValue:IsA("BoolValue") then
    return
  end
  exhaustedValue.Name = "Exhausted"
  exhaustedValue.Value = false
  exhaustedValue.Parent = movementFolder

  -- Track in memory
  playerStamina[player] = staminaValue.Value
  exhaustionData[player] = {
    timestamps = {},
    isExhausted = false,
    exhaustEndTime = 0,
  }

  staminaValue.Changed:Connect(function()
    playerStamina[player] = staminaValue.Value
    staminaUpdate:FireClient(player, staminaValue.Value)
  end)
end

-- Clean up on player leaving
local function cleanupPlayer(player: Player)
  playerStamina[player] = nil
  exhaustionData[player] = nil
end

-- ============================================================================
-- PUBLIC METHODS
-- ============================================================================

-- Call this once per frame or interval to update stamina per player
function StaminaManager.Update(deltaTime: number)
  for player, stamina in pairs(playerStamina) do
    local character = player.Character
    if not character then
      continue
    end

    local humanoid = character:FindFirstChildOfClass("Humanoid")
    if not humanoid then
      continue
    end

    local cgsFolder = player:FindFirstChild("CGS")
    if not cgsFolder then
      continue
    end

    local movementFolder = cgsFolder:FindFirstChild("Movement")
    if not movementFolder then
      continue
    end

    local staminaValue = movementFolder:FindFirstChild("Stamina") :: NumberValue
    local exhaustedValue = movementFolder:FindFirstChild("Exhausted") :: BoolValue
    if not staminaValue or not exhaustedValue then
      continue
    end

    local data = exhaustionData[player]
    local previousStamina = stamina
    local currentStamina = staminaValue.Value

    -- Detect stamina depletion
    if previousStamina > 0 and currentStamina <= 0 then
      table.insert(data.timestamps, tick())
    end

    -- Clean old timestamps
    local currentTime = tick()
    while
      #data.timestamps > 0 and currentTime - data.timestamps[1] > exhaustionConfig.depletionWindow
    do
      table.remove(data.timestamps, 1)
    end

    -- Check for exhaust condition
    if not data.isExhausted and #data.timestamps >= exhaustionConfig.maxDepletions then
      data.isExhausted = true
      data.exhaustEndTime = currentTime + exhaustionConfig.exhaustDuration
      exhaustedValue.Value = true
      exhaustEvent:FireClient(player, true)
    end

    -- End exhaust period
    if data.isExhausted and currentTime > data.exhaustEndTime then
      data.isExhausted = false
      exhaustedValue.Value = false
      exhaustEvent:FireClient(player, false)
    end

    -- Update stamina
    local regenRate = Config.StaminaRegenRate
      * (data.isExhausted and exhaustionConfig.regenMultiplierWhenExhausted or 1)
    if
      humanoid.MoveDirection.Magnitude > 0
      and humanoid.WalkSpeed > Config.WalkSpeed
      and currentStamina > 0
    then
      staminaValue.Value =
        math.clamp(currentStamina - (Config.SprintStaminaDrain * deltaTime), 0, Config.MaxStamina)
    elseif currentStamina < Config.MaxStamina then
      staminaValue.Value =
        math.clamp(currentStamina + (regenRate * deltaTime), 0, Config.MaxStamina)
    end

    -- Enforce walk speed during exhaustion or no stamina
    if data.isExhausted or currentStamina <= 0 then
      humanoid.WalkSpeed = math.min(humanoid.WalkSpeed, Config.WalkSpeed)
    end
  end
end

-- API: Request stamina consumption (returns true if allowed, false if insufficient)
function StaminaManager.RequestConsume(player: Player, amount: number): boolean
  local stamina = playerStamina[player]
  if not stamina or stamina < amount then
    return false
  end

  local cgsFolder = player:FindFirstChild("CGS")
  if not cgsFolder then
    return false
  end

  local movementFolder = cgsFolder:FindFirstChild("Movement")
  local staminaValue = movementFolder:FindFirstChild("Stamina") :: NumberValue
  if not staminaValue then
    return false
  end

  staminaValue.Value = staminaValue.Value - amount
  return true
end

-- Initialization: Setup stamina tracking for existing players
function StaminaManager.Initialize()
  for _, player in pairs(Players:GetPlayers()) do
    createStaminaForPlayer(player)
  end

  Players.PlayerAdded:Connect(createStaminaForPlayer)
  Players.PlayerRemoving:Connect(cleanupPlayer)

  jumpRequest.OnServerEvent:Connect(function(player)
    local character = player.Character
    if not character then
      return
    end
    local humanoid = character:FindFirstChildOfClass("Humanoid")
    if not humanoid then
      return
    end
    if StaminaManager.RequestConsume(player, Config.JumpStaminaCost) then
      humanoid.Jump = true
    end
  end)

  Utils.log(TAG, "Stamina Manager Initialized")
end

-- ============================================================================
-- EXPORTS
-- ============================================================================
return StaminaManager
