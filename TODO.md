# 1. Movement Sub-System

## 1.1 Overview

This subsystem powers immersive and responsive movement tailored for atmospheric, horror-themed gameplay (SCP environments). It supports and resosrts to realism, stealth, and traversal by prioritizing smooth transitions and player control.

This ensures secure, synced movement across both client and server with minimal latency or desync.

---

## 1.2 Objectives

- Enable high-fidelity, semi-realistic movement mechanics suitable for horror scenarios.
- Support stealth and agility trade-offs (stamina for sprinting & jumping, crouch-to-prone stealth flow).
- Prevent exploits through server-side checks and anti-speed manipulation (optional).
- Be modular and extensible for future features (vaulting).
- Directional Body Movement -
  - Body moves sideways if pressing A/D
  - Body moves forward if pressing W
  - Body moves backwards if pressing S

---

## 1.3 Supported Movement States

| State      | Description             | Speed     | Visibility | Sound Volume  |
|------------|-------------------------|-----------|------------|---------------|
| **Idle**   | No movement input       | 0         | Moderate   | Silent        |
| **Walk**   | Default movement        | Low       | Moderate   | Moderate      |
| **Sprint** | Fast movement           | High      | High       | Loud          |
| **Crouch** | Lowered stance          | Slow      | Low        | Quiet         |
| **Prone**  | Lying flat on ground     | Very Slow | Very Low   | Very Quiet    |
| **Jump**   | Short vertical impulse  | -         | High       | Loud          |

---

## 1.4 System Design

This movement system encourages strategic decisions through the trade-off of speed, stamina, and stealth.

### 1.4.1 Purpose

Deliver immersive movement mechanics with meaningful player choices:

- Faster movement drains stamina.
- Stealth modes reduce speed but also lower visibility and noise.

### 1.4.2 Physics & Momentum

Movement responds realistically based on player input direction:

- **W (Forward)**: Builds forward momentum and reaches full speed for that state.
- **A / D (Strafe Left/Right)**: Builds lateral momentum, capped at ~15% of max speed.
- **S (Backward)**: Minimal momentum, capped at ~50% of max speed.

This lightweight physics model simulates realistic acceleration and directionality with low performance cost — ideal for Roblox environments.

## 1.5 Todo

- Procedural movement with replicated animations (animations not handled by server for performance)
- Directional movement with replicated animations (animations not handled by server for performance)
- Analog Movement with replicated animations (animations not handled by server for performance)

## 1.6 explanation of the tasks

Got it. You're organizing your game movement system as a modular, clean architecture where each type of movement is its **own module**, imported and managed by a central **local controller script**. You also want:

- Animations to be **clientside**, but **visible to other clients nearby** (via replication within a certain distance).
- Structure to be modular and scalable.
- Movement to be player-driven, with potential **AI reuse** later.

Below is an explanation structured **for other developers or AI collaborators** who may join the project:

---

## 🧠 System Overview – Movement Modules for SCP/Horror Game

This system handles **realistic, stealth-aware character movement** using three key subsystems — all modular, clean, and designed for extensibility. Each movement type is **isolated in a module**, and a master `MovementController` local script coordinates all the logic per-player.

### 🎯 High-Level Goals

- **Immersive horror movement** (with stealth, momentum, realism)
- Modular codebase (one module per movement type)
- **Client-authoritative animation**, **replicated visuals** (others see your animations within range)
- Anti-exploit capable (server-side velocity/state verification)

---

## 📦 Modules Breakdown

Each module resides in `StarterPlayerScripts.Modules.Movement` or similar and exposes a standard interface.

---

### 1. 🦿 Procedural Leg Animation (R6 Legs Only)

#### Description

This module handles **procedural leg movement** for R6 characters using math (sin/cos) and velocity data — similar to Half-Life/Source engine movement. **No animation assets** used — legs move dynamically.

#### Key Features

- Procedural stepping based on speed and time
- Only applies to `RightLeg` and `LeftLeg` using `Motor6D.Transform`
- Designed for R6 rigs
- 100% client-side
- **Replicates leg pose every 0.2s** within 30 studs via RemoteEvents if enabled

#### Interface

```lua
ProceduralLegs:Init(character)
ProceduralLegs:Update(dt, velocity)
ProceduralLegs:Destroy()
```

---

### 2. 🔁 Directional Movement + Animation

#### Description

Handles directional movement mapping:

- Forward → `walk_forward` animation
- Back → `walk_back`
- Left/Right → `strafe_left/right`
- Idle → `idle`

Animations play based on **velocity direction**, not input.

#### Key Features

- Smooth direction-based animation switching
- Client-side animation control
- Plays AnimationTracks locally
- Replicates `MovementDirection` + `AnimationState` to nearby clients

#### Interface

```lua
DirectionalMovement:Init(character, animator)
DirectionalMovement:Update(moveDir: Vector3)
DirectionalMovement:Destroy()
```

---

### 3. 🎮 Analog Movement System (Core Controller)

#### Description

This is the **core movement engine** that interprets **analog inputs** (joysticks or keyboard smoothing) and transitions between states:

- Idle, Walk, Sprint, Crouch, Prone, Jump

Includes stamina drain, noise emission, momentum simulation, and optional vault/jump support.

#### Key Features

- Smooth analog control (keyboard + joystick)
- Realistic inertia model for horror pacing
- Supports crouch-to-prone, stamina system
- State replicated to server for anticheat + animation visibility
- Modular hook-ins for sound, animation, stamina, and camera shake

#### Interface

```lua
AnalogMovement:Init(character)
AnalogMovement:Update(inputVector: Vector2, dt: number)
AnalogMovement:GetState() → "Walk" | "Sprint" | "Crouch" | etc.
AnalogMovement:Destroy()
```

---

## 🔄 Animation Replication

Since **Humanoid animations don’t replicate if played locally**, we fake replication by:

1. On **client**, play animations locally.
2. Send a **RemoteEvent** every \~0.25s with:

   ```lua
   {
     player = LocalPlayer,
     animation = "Walk",
     direction = Vector3,
     movementState = "Crouch"
   }
   ```

3. On **other clients**, display those animations only if:

   - Distance < 30 studs
   - Character still exists
4. Optional LOD: skip anim replication if off-screen or obscured

This keeps bandwidth low while letting others see your movement style.

---

## 🧩 Master Controller (LocalScript)

Resides in `StarterPlayerScripts`.

### Purpose

- Initialize character
- Import and glue modules together
- Poll inputs
- Feed each movement module
- Clean up on death/reset

### Example

```lua
local ProceduralLegs = require(...Modules.Movement.ProceduralLegs)
local DirectionalMovement = require(...Modules.Movement.DirectionalMovement)
local AnalogMovement = require(...Modules.Movement.AnalogMovement)

local character = game.Players.LocalPlayer.Character
local animator = character:WaitForChild("Humanoid"):WaitForChild("Animator")

ProceduralLegs:Init(character)
DirectionalMovement:Init(character, animator)
AnalogMovement:Init(character)

RunService.RenderStepped:Connect(function(dt)
  local moveVector = getAnalogInput() -- returns Vector2
  AnalogMovement:Update(moveVector, dt)

  local velocity = character.HumanoidRootPart.Velocity
  local state = AnalogMovement:GetState()

  DirectionalMovement:Update(velocity.Unit)
  ProceduralLegs:Update(dt, velocity)

  -- Optional: replicate animation state
  if tick() - lastReplicated > 0.25 then
    ReplicateAnimationState(state, velocity)
    lastReplicated = tick()
  end
end)
```

---

## 🛠 If Modularity Fails…

If for any reason modules **can’t work (e.g., due to timing or shared state limitations)**, fall back to a **single LocalScript** with:

- Sections split cleanly (`-- Procedural Section`, `-- Analog Logic`, etc.)
- Everything handled inside one `RenderStepped` loop
- Local module-style functions per subsystem

---

## 🧠 Summary

| System               | Module?  | Animation Replication | Server Role            |
| -------------------- | -------  | --------------------- | -----------------------|
| Procedural Legs      | ✅       | ✅ (remote every 0.2s)| ❌ (visual only)      |
| Directional Movement | ✅       | ✅ (broadcasted state)| ❌                    |
| Analog Movement      | ✅       | ✅                    | ✅ (verify movement)  |
| Controller Script    | ✅       | 🚦Central hub         | ❌                    |
